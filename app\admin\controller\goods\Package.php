<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\admin\controller\goods;

use app\common\basics\AdminBase;
use app\common\model\goods\Qualification as QualificationModel;
use app\model\goods\QualificationPackage;
use app\model\goods\QualificationPackageItem;
use app\common\server\JsonServer;
use think\facade\Db;
use think\facade\View;

/**
 * 资质包管理
 * Class Package
 * @package app\admin\controller\goods
 */
class Package extends AdminBase
{
    /**
     * 列表
     */
    public function lists()
    {
        if ($this->request->isAjax()) {
            $param = $this->request->get();
            $limit = $param['limit'] ?? 10;
            $model = new QualificationPackage();
            $list = $model->with('qualifications')->paginate($limit);
            return JsonServer::success('获取成功', $list);
        }
        return view();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $param = $this->request->post();
            $qualification_ids = $param['qualification_ids'] ?? [];
            if (empty($qualification_ids)) {
                return JsonServer::error('请至少选择一个资质证书');
            }

            Db::startTrans();
            try {
                $package = QualificationPackage::create($param);
                $items = [];
                foreach ($qualification_ids as $id) {
                    $items[] = [
                        'package_id' => $package->id,
                        'qualification_id' => $id
                    ];
                }
                (new QualificationPackageItem())->saveAll($items);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return JsonServer::error('添加失败: ' . $e->getMessage());
            }
            return JsonServer::success('添加成功');
        }

        $qualificationModel = new QualificationModel();
        $qualifications = $qualificationModel->where('del', 0)->select();
        View::assign('qualifications', $qualifications);
        return view();
    }

    /**
     * 编辑
     */
    public function edit()
    {
        $id = $this->request->get('id');
        $model = new QualificationPackage();
        $info = $model->with(['items'])->find($id);
        if (empty($info)) {
            return view('error/404');
        }

        if ($this->request->isPost()) {
            $param = $this->request->post();
            $qualification_ids = $param['qualification_ids'] ?? [];
            if (empty($qualification_ids)) {
                return JsonServer::error('请至少选择一个资质证书');
            }

            Db::startTrans();
            try {
                $info->save($param);
                QualificationPackageItem::where('package_id', $id)->delete();
                $items = [];
                foreach ($qualification_ids as $q_id) {
                    $items[] = [
                        'package_id' => $id,
                        'qualification_id' => $q_id
                    ];
                }
                (new QualificationPackageItem())->saveAll($items);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return JsonServer::error('编辑失败: ' . $e->getMessage());
            }
            return JsonServer::success('编辑成功');
        }

        $qualificationModel = new QualificationModel();
        $qualifications = $qualificationModel->where('del', 0)->select();
        $checked_ids = array_column($info->items->toArray(), 'qualification_id');

        View::assign('info', $info);
        View::assign('qualifications', $qualifications);
        View::assign('checked_ids', $checked_ids);
        return view();
    }

    /**
     * 删除
     */
    public function del()
    {
        $id = $this->request->post('id');
        $model = new QualificationPackage();
        Db::startTrans();
        try {
            $model->find($id)->delete();
            // 同时删除关联的项目
            QualificationPackageItem::where('package_id', $id)->delete();
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return JsonServer::error('删除失败: ' . $e->getMessage());
        }
        return JsonServer::success('删除成功');
    }

    /**
     * 状态切换
     */
    public function status()
    {
        $post = $this->request->post();
        $data = [
            $post['field'] => $post['value'],
            'update_time' => time()
        ];

        $result = QualificationPackage::where('id', $post['id'])->update($data);
        if ($result) {
            return JsonServer::success('操作成功');
        }
        return JsonServer::error('操作失败');
    }

    /**
     * 获取所有启用的资质包（用于其他模块调用）
     */
    public function getEnabled()
    {
        $packages = QualificationPackage::with(['qualifications'])
            ->where(['del' => 0, 'status' => 1])
            ->order('sort', 'asc')
            ->select();

        return JsonServer::success('获取成功', $packages);
    }
}
