{layout name="layout1" /}
<!-- 样式 -->
<style>
  .layui-table-cell {
    height: auto;
    overflow: visible; 
  }

  /* 内联编辑样式 */
  .name-text:hover, .sort-display:hover {
    background-color: #f0f9ff;
    border: 1px dashed #009688;
    cursor: pointer;
  }

  /* 编辑输入框的通用样式 */
  .inline-edit-input {
    position: absolute; /* 使用绝对定位，脱离文档流 */
    box-sizing: border-box;
    background-color: #fff;
    border: 1px solid #009688;
    box-shadow: 0 0 5px rgba(0, 150, 136, 0.5);
    z-index: 19999; /* 确保在顶层 */
    padding: 5px;
    margin: 0;
    text-align: left;
  }

  .inline-edit-input:focus {
    outline: none;
    border-color: #009688;
    box-shadow: 0 0 8px rgba(0, 150, 136, 0.5);
  }

  .name-text, .sort-display {
    display: inline-block;
    vertical-align: middle;
    transition: all 0.2s ease;
    min-height: 20px;
    line-height: 20px;
  }
</style>
<!-- 操作提示 -->
<div class="layui-fluid">
  <div class="layui-card" style="margin-top: 15px;">
    <div class="layui-card-body">
      <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
        <div class="layui-colla-item">
          <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
          <div class="layui-colla-content layui-show">
            <p>*平台商品分类，商家发布商品的时候需要选择对应的平台商品分类，用户可以根据商品分类搜索商品。</p>
            <p style="color: #009688;">*快速编辑：双击分类名称可直接编辑，点击排序数字可直接编辑，按Enter保存，按Esc取消。</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 功能按钮 -->
    <div class="layui-form layui-card-header layuiadmin-card-header-auto">
      <div class="layui-form-item">
        <div class="layui-btn-container" style="display: inline-block;">
          <div class="layui-btn-group">
            <button class="layui-btn layui-btn-sm layui-btn-goods_category {$view_theme_color}"
              id="goods_category-add">新增商品分类</button>
            <button class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}"
              id="expand-all">全部展开</button>
            <button class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}"
              id="fold-all">全部折叠</button>
          </div>
        </div>
        <input type="text" id="search-value" placeholder="分类名称" autocomplete="off" class="layui-input"
          style="display: inline-block;width: 140px;height: 30px;padding: 0 5px;margin-right: 5px;">
        <div class="layui-btn-container" style="display: inline-block;">
          <button id="search" class="layui-btn layui-btn-sm  layui-btn-goods_category {$view_theme_color}">
            <i class="layui-icon">&#xe615;</i>搜索
          </button>
        </div>
      </div>
    </div>
    
    <div class="layui-card-body">
      <!-- 树形表格 -->
      <table id="goods_category-lists" lay-filter="goods_category-lists"></table>
      <!-- 分类图标 -->
      <script type="text/html" id="image">
        {{#  if(d.image != ''){ }}
          <img src="{{d.image}}" style="height:80px;width:80px" class="image-show">
        {{#  } }} 
      </script>
      <!-- 商家端可选 -->
      <script type="text/html" id="shop_visible">
        <input type="checkbox"  lay-filter="switch-shop_visible" data-id={{d.id}} data-field='shop_visible'   lay-skin="switch" lay-text="可选|不可选" {{#  if(d.shop_visible){ }} checked  {{# } }} />
      </script>
      <!-- API显示 -->
      <script type="text/html" id="api_visible">
        <input type="checkbox"  lay-filter="switch-api_visible" data-id={{d.id}} data-field='api_visible'   lay-skin="switch" lay-text="显示|隐藏" {{#  if(d.api_visible){ }} checked  {{# } }} />
      </script>
      <!-- 关联资质 -->
      <script type="text/html" id="qualification">
        {{#  if(d.qualification_names){ }}
          <span style="color: #5FB878; font-size: 12px;">{{d.qualification_names}}</span>
        {{#  } else { }}
          <span style="color: #999; font-size: 12px;">未关联资质</span>
        {{#  } }}
      </script>
      <!-- 分类名称内联编辑模板 -->
      <script type="text/html" id="name-edit">
        <span class="name-text" style="cursor: text; border-radius: 3px;" title="双击编辑名称">{{d.name}}</span>
      </script>
      <!-- 排序内联编辑模板 -->
      <script type="text/html" id="sort-edit">
        <span class="sort-display" style="padding: 3px 6px; display: inline-block; min-width: 30px; text-align: center; border-radius: 3px;" title="点击编辑排序">{{d.sort}}</span>
      </script>
      <!-- 操作列 -->
      <script type="text/html" id="goods_category-operation">
        <a class="layui-btn layui-btn-normal layui-btn-sm" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-warm layui-btn-sm" lay-event="qualification">资质包</a>
        <a class="layui-btn layui-btn-danger layui-btn-sm" lay-event="del">删除</a>
      </script>
    </div>
  </div>
</div>

<script>
  layui.config({
    version: "{$front_version}",
    base: '/static/lib/'
  }).extend({
    treeTable: 'treetable/treeTable'
  }).use(['layer', 'treeTable', 'form', 'element'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var treeTable = layui.treeTable;

    // 渲染树形表格
    var insTb = treeTable.render({
      elem: '#goods_category-lists',
      tree: {
        iconIndex: 0, 
        childName: 'sub', 
        getIcon: function (d) {
          return '<i class="ew-tree-icon layui-icon layui-icon-spread-left "></i>';
        }
      },
      cols: [
        { field: 'name', title: '分类名称', width: 280, templet: '#name-edit'},
        { title: '分类图标', width: 120, align: 'center', templet: '#image'},
        { field: 'qualification_names', title: '关联资质', width: 200, align: 'center', templet: '#qualification'},
        { title: '商家可选', width: 100, align: 'center', templet: '#shop_visible' },
        { title: 'API显示', width: 100, align: 'center', templet: '#api_visible' },
        { field: 'sort', title: '排序', width: 80, align: 'center', templet: '#sort-edit' },
        { fixed: 'right', align: 'center', toolbar: '#goods_category-operation', title: '操作'}
      ],
      reqData: function(data, callback) {
        like.ajax({
          url:'{:url("goods.category/lists")}',
          type:'get',
          success:function (res) {
            jsonObj = JSON.parse(res.data);
            if(res.code==0) callback(jsonObj);
            else callback(res.msg);
          }
        })
      }
    });

    // ------------------- 其他事件绑定 (保持不变) -------------------
    $('#goods_category-add').click(function () {
      layer.open({
        type: 2, title: '新增商品分类', content: '{:url("goods.category/add")}', area: ['90%', '90%'],
        btn: ['确认', '返回'], btnAlign: 'c',
        yes: function (index, layero) {
          var iframeWindow = window['layui-layer-iframe' + index], submitID = 'add-goods_category-submit', submit = layero.find('iframe').contents().find('#' + submitID);
          iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
            like.ajax({
              url: '{:url("goods.category/add")}', data: data.field, type: "post",
              success: function (res) {
                if (res.code == 1) {
                  layer.msg(res.msg, { icon: 1, time: 1000 });
                  layer.close(index);
                }
              }
            });
          });
          submit.trigger('click');
        }
      })
    });
    treeTable.on('tool(goods_category-lists)', function (obj) {
      if (obj.event === 'del') {
        layer.confirm('确定删除商品分类:' + '<span style="color: red">' + obj.data.name + '</span>', function (index) {
          like.ajax({
            url: '{:url("goods.category/del")}', data: { id: obj.data.id }, type: 'post', dataType: 'json',
            success: function (res) {
              if (res.code === 1) {
                layer.msg(res.msg, { icon: 1, time: 1000 }, function() { layer.close(index); });
              }
            }
          })
        })
      }
      if (obj.event === 'edit') {
        layer.open({
          type: 2, title: '编辑商品分类', content: '{:url("goods.category/edit")}?id=' + obj.data.id, area: ['90%', '90%'],
          btn: ['确定', '取消'], btnAlign: 'c',
          yes: function (index, layero) {
            var iframeWindow = window['layui-layer-iframe' + index], submitID = 'edit-goods_category-submit', submit = layero.find('iframe').contents().find('#' + submitID);
            iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
              like.ajax({
                url: '{:url("goods.category/edit")}', data: data.field, type: "post",
                success: function (res) {
                  if (res.code == 1) {
                    layer.msg(res.msg, { icon: 1, time: 1000 }, function() { layer.close(index); });
                  }
                }
              });
            });
            submit.trigger('click');
          }
        })
      }
      if (obj.event === 'qualification') {
        layer.open({
          type: 2,
          title: '分类资质包设置 - ' + obj.data.name,
          content: '{:url("goods.category/qualificationPackage")}?id=' + obj.data.id,
          area: ['80%', '80%'],
          btn: ['确定', '取消'],
          btnAlign: 'c',
          yes: function (index, layero) {
            var iframeWindow = window['layui-layer-iframe' + index],
                submitID = 'qualification-package-submit',
                submit = layero.find('iframe').contents().find('button[lay-filter="' + submitID + '"]');

            iframeWindow.layui.form.on('submit(' + submitID + ')', function (data) {
              like.ajax({
                url: '{:url("goods.category/qualificationPackage")}',
                data: data.field,
                type: "post",
                success: function (res) {
                  if (res.code == 1) {
                    layer.msg(res.msg, { icon: 1, time: 1000 }, function() {
                      layer.close(index);
                      // 刷新表格数据
                      treeTable.reload('goods_category-lists');
                    });
                  }
                }
              });
            });
            submit.trigger('click');
          }
        })
      }
    });
    form.on('switch(switch-shop_visible)', function (obj) {
      var id = $(obj.elem).data('id'), status = obj.elem.checked ? 1 : 0;
      like.ajax({
        url: '{:url("goods.category/switchShopVisible")}', data: { id: id, status: status }, type: 'post',
        success: function (res) { layer.msg(res.msg, { icon: res.code == 1 ? 1 : 2, time: 1000 }); }
      })
    });
    form.on('switch(switch-api_visible)', function (obj) {
      var id = $(obj.elem).data('id'), status = obj.elem.checked ? 1 : 0;
      like.ajax({
        url: '{:url("goods.category/switchApiVisible")}', data: { id: id, status: status }, type: 'post',
        success: function (res) { layer.msg(res.msg, { icon: res.code == 1 ? 1 : 2, time: 1000 }); }
      })
    });
    $('#expand-all').click(function () { insTb.expandAll(); });
    $('#fold-all').click(function () { insTb.foldAll(); });
    $('#search').click(function () {
      var keywords = $('#search-value').val();
      keywords ? insTb.filterData(keywords) : insTb.clearFilter();
    });

    // ------------------- 内联编辑功能 (支持名称和排序) -------------------
    var $editInput = null;
    var currentEditTarget = null;

    function initEditInput() {
        if (!$editInput) {
            $editInput = $('<input class="inline-edit-input" />');
            $('body').append($editInput);

            $editInput.on('keypress', function(e) {
                if (e.which === 13) { e.preventDefault(); $(this).blur(); }
            }).on('keyup', function(e) {
                if (e.which === 27) { e.preventDefault(); cancelEdit(); }
            }).on('blur', function() {
                saveEdit();
            });
        }
    }

    function startEdit($targetSpan, inputType) {
        if ($editInput && $editInput.is(':visible')) {
            $editInput.blur();
        }
        
        initEditInput();
        currentEditTarget = $targetSpan;
        var text = $targetSpan.text().trim();
        var offset = $targetSpan.offset();
        var width = '140px';
        var height = $targetSpan.outerHeight();

        $editInput.attr('type', inputType || 'text')
            .val(text)
            .css({
                top: offset.top,
                left: offset.left,
                width: width,
                height: height,
                'font-size': $targetSpan.css('font-size'),
                'font-family': $targetSpan.css('font-family'),
                'line-height': height + 'px',
                'text-align': $targetSpan.css('text-align')
            })
            .show()
            .focus()
            .select();
        
        $targetSpan.css('visibility', 'hidden');
    }

    function saveEdit() {
        if (!currentEditTarget || !$editInput || !$editInput.is(':visible')) return;

        var newValue = $editInput.val().trim();
        var oldValue = currentEditTarget.text().trim();
        var id = currentEditTarget.closest('tr').data('id');
        var field = currentEditTarget.hasClass('name-text') ? 'name' : 'sort';

        if (newValue && newValue !== oldValue) {
            saveFieldValue(id, field, newValue, function(success) {
                if (success) {
                    currentEditTarget.text(newValue);
                    updateTableData(id, field, newValue);
                }
                finishEdit();
            });
        } else {
            finishEdit();
        }
    }

    function cancelEdit() {
        if (!currentEditTarget) return;
        finishEdit();
    }
    
    function finishEdit() {
        if (currentEditTarget) {
            currentEditTarget.css('visibility', 'visible');
            currentEditTarget = null;
        }
        if ($editInput) {
            $editInput.hide();
        }
    }

    // 事件绑定
    $(document).on('dblclick', '.name-text', function(e) {
        e.stopPropagation();
        startEdit($(this), 'text');
    });

    $(document).on('click', '.sort-display', function(e) {
        e.stopPropagation();
        startEdit($(this), 'number');
    });

    $(document).on('click', function(e) {
        if ($editInput && $editInput.is(':visible') && !$(e.target).is($editInput)) {
            $editInput.blur();
        }
    });

    function saveFieldValue(id, field, value, callback) {
        $.ajax({
            url: '{:url("goods.category/updateField")}',
            data: { id: id, field: field, value: value },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                layui.layer.msg('保存成功', { icon: 1, time: 1000 });
                    callback(true);
            },
            error: function() {
                layui.layer.msg('网络错误，保存失败', { icon: 2, time: 2000 });
                callback(false);
            }
        });
    }

    function updateTableData(id, field, value) {
        var tableData = insTb.config.data;
        function updateNode(nodes) {
            for (var i = 0; i < nodes.length; i++) {
                if (nodes[i].id == id) {
                    nodes[i][field] = value;
                    return true;
                }
                if (nodes[i].sub && nodes[i].sub.length > 0) {
                    if (updateNode(nodes[i].sub)) return true;
                }
            }
            return false;
        }
        updateNode(tableData);
    }
  });
</script>