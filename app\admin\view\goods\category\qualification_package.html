{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>分类资质包设置 - {$category.name}</h3>
        </div>
        
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*为分类设置资质包后，该分类下的商品将继承这些资质要求。</p>
                        <p>*商品可以选择使用独立的资质设置来覆盖分类的设置。</p>
                        <p>*资质包包含一个或多个资质证书，可以设置为"全部需要"或"任选其一"。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <form class="layui-form" lay-filter="qualification-package-form">
                <input type="hidden" name="id" value="{$category.id}">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">选择资质包：</label>
                    <div class="layui-input-block">
                        {if condition="!empty($packages)"}
                            {volist name="packages" id="package"}
                            <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #e6e6e6; border-radius: 4px;">
                                <div style="margin-bottom: 8px;">
                                    <input type="checkbox" name="package_ids[]" value="{$package.id}" 
                                           {if condition="in_array($package.id, $currentPackageIds)"}checked{/if}
                                           lay-skin="primary" title="{$package.name}">
                                </div>
                                
                                <div style="margin-left: 20px; color: #666; font-size: 12px;">
                                    <div>选择模式: {$package.selection_mode.text}</div>
                                    <div>是否必选: {if condition="$package.is_required == 1"}必选{else/}可选{/if}</div>
                                    {if condition="!empty($package.qualifications)"}
                                    <div>包含资质: 
                                        {volist name="package.qualifications" id="qual"}
                                            <span class="layui-badge layui-bg-blue" style="margin: 2px;">{$qual.name}</span>
                                        {/volist}
                                    </div>
                                    {/if}
                                    {if condition="!empty($package.remark)"}
                                    <div>说明: {$package.remark}</div>
                                    {/if}
                                </div>
                            </div>
                            {/volist}
                        {else/}
                            <div class="layui-text" style="color: #999;">
                                暂无可用的资质包，请先 <a href="{:url('goods.package/add')}" target="_blank">创建资质包</a>
                            </div>
                        {/if}
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="qualification-package-submit">保存设置</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
layui.use(['form'], function(){
    var form = layui.form;
    
    form.on('submit(qualification-package-submit)', function(data){
        var field = data.field;
        
        like.ajax({
            url: '{:url("goods.category/qualificationPackage")}',
            data: field,
            type: "post",
            success: function(res) {
                if(res.code == 1) {
                    layui.layer.msg(res.msg, { icon: 1, time: 1000 }, function(){
                        history.back();
                    });
                } else {
                    layui.layer.msg(res.msg, { icon: 2, time: 2000 });
                }
            }
        });
        
        return false;
    });
});
</script>
