{layout name="layout1" /}
<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-header">
            <h3>商品资质包设置 - {$goods.name}</h3>
        </div>
        
        <!--操作提示-->
        <div class="layui-card-body">
            <div class="layui-collapse like-layui-collapse" lay-accordion="" style="border:1px dashed #c4c4c4">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                    <div class="layui-colla-content layui-show">
                        <p>*商品可以选择继承分类的资质设置，或使用独立的资质设置。</p>
                        <p>*开启"使用独立资质设置"后，商品将不再继承分类的资质要求。</p>
                        <p>*资质包包含一个或多个资质证书，可以设置为"全部需要"或"任选其一"。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-card-body">
            <form class="layui-form" lay-filter="goods-qualification-form">
                <input type="hidden" name="goods_id" value="{$goods.id}">
                
                <!-- 资质设置开关 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">资质设置：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="use_custom_qualification" value="0" title="继承分类设置" 
                               {if condition="$goods.use_custom_qualification == 0"}checked{/if}>
                        <input type="radio" name="use_custom_qualification" value="1" title="使用独立设置" 
                               {if condition="$goods.use_custom_qualification == 1"}checked{/if}>
                    </div>
                </div>

                <!-- 分类继承的资质包显示 -->
                <div class="layui-form-item" id="category-packages" {if condition="$goods.use_custom_qualification == 1"}style="display:none;"{/if}>
                    <label class="layui-form-label">分类资质包：</label>
                    <div class="layui-input-block">
                        {if condition="!empty($categoryPackages)"}
                            {volist name="categoryPackages" id="package"}
                            <div style="margin-bottom: 10px; padding: 8px; background-color: #f8f8f8; border-radius: 4px;">
                                <div style="font-weight: bold; color: #333;">{$package.name}</div>
                                <div style="font-size: 12px; color: #666;">
                                    选择模式: {$package.selection_mode.text} | 
                                    是否必选: {if condition="$package.is_required == 1"}必选{else/}可选{/if}
                                </div>
                                {if condition="!empty($package.qualifications)"}
                                <div style="margin-top: 5px;">
                                    {volist name="package.qualifications" id="qual"}
                                        <span class="layui-badge layui-bg-blue" style="margin: 2px;">{$qual.name}</span>
                                    {/volist}
                                </div>
                                {/if}
                            </div>
                            {/volist}
                        {else/}
                            <div style="color: #999;">该商品分类暂未设置资质包要求</div>
                        {/if}
                    </div>
                </div>

                <!-- 独立资质包设置 -->
                <div class="layui-form-item" id="custom-packages" {if condition="$goods.use_custom_qualification == 0"}style="display:none;"{/if}>
                    <label class="layui-form-label">选择资质包：</label>
                    <div class="layui-input-block">
                        {if condition="!empty($packages)"}
                            {volist name="packages" id="package"}
                            <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #e6e6e6; border-radius: 4px;">
                                <div style="margin-bottom: 8px;">
                                    <input type="checkbox" name="packages[{$i}][package_id]" value="{$package.id}" 
                                           lay-skin="primary" title="{$package.name}" class="package-checkbox">
                                </div>
                                
                                <div style="margin-left: 20px; margin-bottom: 10px;">
                                    <label style="font-weight: normal;">
                                        <input type="radio" name="packages[{$i}][is_required]" value="1" title="必选" checked>
                                        <input type="radio" name="packages[{$i}][is_required]" value="0" title="可选">
                                    </label>
                                </div>
                                
                                <div style="margin-left: 20px; color: #666; font-size: 12px;">
                                    <div>选择模式: {$package.selection_mode.text}</div>
                                    {if condition="!empty($package.qualifications)"}
                                    <div>包含资质: 
                                        {volist name="package.qualifications" id="qual"}
                                            <span class="layui-badge layui-bg-blue" style="margin: 2px;">{$qual.name}</span>
                                        {/volist}
                                    </div>
                                    {/if}
                                    {if condition="!empty($package.remark)"}
                                    <div>说明: {$package.remark}</div>
                                    {/if}
                                </div>
                            </div>
                            {/volist}
                        {else/}
                            <div class="layui-text" style="color: #999;">
                                暂无可用的资质包，请先 <a href="{:url('goods.package/add')}" target="_blank">创建资质包</a>
                            </div>
                        {/if}
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="goods-qualification-submit">保存设置</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="history.back()">返回</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
layui.use(['form'], function(){
    var form = layui.form;
    
    // 监听资质设置开关变化
    form.on('radio()', function(data){
        if(data.elem.name === 'use_custom_qualification') {
            if(data.value === '0') {
                $('#category-packages').show();
                $('#custom-packages').hide();
            } else {
                $('#category-packages').hide();
                $('#custom-packages').show();
            }
        }
    });
    
    // 监听复选框变化，控制必选/可选单选框的显示
    $('.package-checkbox').on('change', function(){
        var index = $(this).attr('name').match(/\[(\d+)\]/)[1];
        var radioContainer = $(this).closest('.layui-form-item').find('input[name="packages[' + index + '][is_required]"]').closest('label');
        
        if($(this).is(':checked')) {
            radioContainer.show();
        } else {
            radioContainer.hide();
        }
    });
    
    form.on('submit(goods-qualification-submit)', function(data){
        var field = data.field;
        
        // 处理复选框数据
        var packages = [];
        $('.package-checkbox:checked').each(function(){
            var index = $(this).attr('name').match(/\[(\d+)\]/)[1];
            var packageId = $(this).val();
            var isRequired = $('input[name="packages[' + index + '][is_required]"]:checked').val() || 1;
            
            packages.push({
                package_id: packageId,
                is_required: isRequired
            });
        });
        
        field.packages = packages;
        
        like.ajax({
            url: '{:url("goods.goods/qualificationPackage")}',
            data: field,
            type: "post",
            success: function(res) {
                if(res.code == 1) {
                    layui.layer.msg(res.msg, { icon: 1, time: 1000 }, function(){
                        history.back();
                    });
                } else {
                    layui.layer.msg(res.msg, { icon: 2, time: 2000 });
                }
            }
        });
        
        return false;
    });
    
    // 初始化已选择的资质包
    {if condition="!empty($currentOverrides)"}
    {volist name="currentOverrides" id="override"}
    $('input[value="{$override.package_id}"]').prop('checked', true);
    $('input[name*="[is_required]"][value="{$override.is_required}"]').prop('checked', true);
    {/volist}
    form.render();
    {/if}
});
</script>
