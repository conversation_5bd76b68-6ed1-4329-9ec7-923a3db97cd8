{layout name="layout2" /}
<style>
  .layui-form-label {
    color: #6a6f6c;
    width: 140px;
  }
  .layui-input-block{
    margin-left:170px;
  }
  .reqRed::before {
    content: '*';
    color: red;
  }
  .qualification-list {
    border: 1px solid #e6e6e6;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
  }
</style>
<div class="layui-form" style="padding: 20px 30px 0 0;">
  <input type="hidden" name="id" value="{$info.id}">
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">资质包名称：</label>
    <div class="layui-input-block">
      <input type="text" name="name" value="{$info.name}" lay-verify="required" lay-verType="tips" placeholder="请输入资质包名称" autocomplete="off" class="layui-input">
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">资质包描述：</label>
    <div class="layui-input-block">
      <textarea name="remark" placeholder="请输入资质包描述" class="layui-textarea">{$info.remark}</textarea>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label reqRed">包含资质：</label>
    <div class="layui-input-block qualification-list">
        {if empty($qualifications)}
            <div style="text-align: center; color: #999;">暂无可用资质，请先在“资质管理”中添加</div>
        {else}
            {foreach $qualifications as $item}
            <input type="checkbox" name="qualification_ids[]" value="{$item.id}" title="{$item.name}" {if in_array($item.id, $checked_ids)}checked{/if}>
            {/foreach}
        {/if}
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">选择模式：</label>
    <div class="layui-input-block">
      <input type="radio" name="selection_mode" value="1" title="全部需要" {if $info.selection_mode.value == 1}checked{/if}>
      <input type="radio" name="selection_mode" value="2" title="任选其一" {if $info.selection_mode.value == 2}checked{/if}>
      <div class="layui-form-mid layui-word-aux">“全部需要”表示商家必须提交包内所有资质；“任选其一”表示商家只需提交包内任意一个资质即可。</div>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">是否必选：</label>
    <div class="layui-input-block">
      <input type="radio" name="is_required" value="1" title="必选" {if $info.is_required.value == 1}checked{/if}>
      <input type="radio" name="is_required" value="0" title="可选" {if $info.is_required.value == 0}checked{/if}>
      <div class="layui-form-mid layui-word-aux">“必选”的资质包，商家必须满足其条件才能发布相关商品。</div>
    </div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">排序：</label>
    <div class="layui-input-inline">
      <input type="number" name="sort" value="{$info.sort}" min="0" class="layui-input">
    </div>
    <div class="layui-form-mid layui-word-aux">数值越小，排序越靠前</div>
  </div>
  <div class="layui-form-item">
    <label class="layui-form-label">状态：</label>
    <div class="layui-input-block">
      <input type="radio" name="status" value="1" title="启用" {if $info.status == 1}checked{/if}>
      <input type="radio" name="status" value="0" title="禁用" {if $info.status == 0}checked{/if}>
    </div>
  </div>
  <div class="layui-form-item layui-hide">
      <input type="button" lay-submit lay-filter="package-submit-edit" id="package-submit-edit" value="确认">
  </div>
</div>
<script>
layui.use(['form', 'like'], function(){
    var form = layui.form;
    form.render();
});
</script>
