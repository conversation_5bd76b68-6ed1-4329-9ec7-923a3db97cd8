<?php



namespace app\common\model\goods;

use app\common\basics\Models;
use app\common\model\distribution\DistributionGoods;
use app\common\model\shop\Shop;
use app\common\server\UrlServer;
use think\facade\Event;


/**
 * 商品-模型
 * Class Goods
 * @package app\common\model\goods
 */
class Goods extends Models
{
    
    // 定义事件
    protected static $event = [
        'before_insert' => 'onBeforeInsert',
        'after_insert'  => 'onAfterInsert',
        'before_update' => 'onBeforeUpdate',
        'after_update'  => 'onAfterUpdate',
        'before_delete' => 'onBeforeDelete',
        'after_delete'  => 'onAfterDelete',
    ];
    /**
     * 商品轮播图 关联模型
     */
    public function GoodsImage()
    {
        return $this->hasMany('GoodsImage', 'goods_id', 'id')->field('goods_id, uri');
    }
    /**
     * 商品SKU 关联模型
     */
    public function GoodsItem()
    {
        return $this->hasMany('GoodsItem', 'goods_id', 'id')
            ->field('id, goods_id, image, spec_value_ids, spec_value_str, market_price, price, stock, chengben_price,pdjc_price');
    }

    /**
     * 店铺 关联模型
     */
    public function Shop()
    {
        return $this->hasOne(Shop::class, 'id', 'shop_id')
            ->field('id, name, logo, type, star, score, intro,is_pay, mobile,is_freeze,is_run,return_rate,expire_time,yan_level,jcshop_vip')
            ->append([ 'is_expire' ]);
    }

    public function getIsDistributionDescAttr($value, $data)
    {
        return $data['is_distribution'] ? '是': '否';
    }

    function getIsMemberDescAttr($value, $data)
    {
        return $data['is_member'] ? '是': '否';
    }

    /**
     * 根据商品id获取商品名称
     */
    public function getGoodsNameById($goods_id)
    {
        return $this->where('id',$goods_id)->value('name');

    }

    /**
     * 根据商品id查询商品是否上架
     */
    public function checkStatusById($goods_id)
    {
        $status = $this
            ->where([
                ['id','=',$goods_id],
                ['del','=',0],
            ])
            ->value('status');
        if ($status){
            if ($status == 1){
                return true;
            }
            if (empty($status) || $status ===0){
                return  false;
            }
        }
        return false;
    }


    /**
     * 根据goods_id查询商品配送方式及所需信息
     */
    public function getExpressType($goods_id)
    {
        return $this->where('id',$goods_id)->column('express_type,express_money,express_template_id')[0];
    }

    /**
     * 最小值与最大值范围
     */
    public function getMinMaxPriceAttr($value, $data)
    {
        return '¥ ' . $data['min_price'] . '~ ¥ '. $data['max_price'];
    }

    /**
     * @notes 商品是否参与分销
     * @param $value
     * @return string
     * <AUTHOR>
     * @date 2021/9/1 17:29
     */
    public function getDistributionFlagAttr($value)
    {
        $data = DistributionGoods::where('goods_id', $value)->findOrEmpty()->toArray();
        if (!empty($data) && $data['is_distribution'] == 1) {
            return true;
        }
        return false;
    }


    /**
     * @notes 商品详情
     * @param $value
     * @param $data
     * @return array|string|string[]|null
     * <AUTHOR>
     * @date 2022/6/13 10:50
     */
    public function getContentAttr($value,$data){
/*        $preg = '/(<img .*?src=")[^https|^http](.*?)(".*?>)/is';*/
//        $local_url = UrlServer::getFileUrl('/');
//        return  preg_replace($preg, "\${1}$local_url\${2}\${3}",$value);
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlGetImage($content);
        }
        return $content;
    }
    public function setContentAttr($value,$data)
    {
        $content = $data['content'];
        if (!empty($content)) {
            $content = HtmlSetImage($content);
        }
        return $content;
    }

    /**
     * @notes 分销状态搜索器
     * @param $query
     * @param $value
     * @param $data
     * <AUTHOR>
     * @date 2021/9/2 9:55
     */
    public function searchIsDistributionAttr($query, $value, $data)
    {
        // 不参与分销
        if (isset($data['is_distribution']) && $data['is_distribution'] == '0') {
            // 先找出参与分销的商品id
            $ids = DistributionGoods::where('is_distribution', 1)->column('goods_id');
            // 在搜索条件中将它们排除掉
            $query->where('id', 'not in', $ids);

        }
        // 参与分销
        if (isset($data['is_distribution']) && $data['is_distribution'] == '1') {
            // 先找出参与分销的商品id
            $ids = DistributionGoods::where('is_distribution', 1)->column('goods_id');
            // 在搜索条件中使用它们来进行过滤
            $query->where('id', 'in', $ids);
        }
    }

    /**
     * 新增前
     */
    public static function onBeforeInsert($model)
    {
        // 可以在这里做一些数据验证或处理
    }

    /**
     * 商品资质覆盖设置 关联模型
     */
    public function qualificationOverrides()
    {
        return $this->hasMany(GoodsQualificationOverride::class, 'goods_id', 'id');
    }

    /**
     * 商品分类 关联模型
     */
    public function category()
    {
        return $this->belongsTo(GoodsCategory::class, 'category_id', 'id');
    }

    /**
     * 获取商品的资质包要求（包括分类继承和商品独立设置）
     */
    public function getQualificationPackages()
    {
        // 如果商品使用独立资质设置
        if ($this->use_custom_qualification == 1) {
            return $this->qualificationOverrides()->with(['package.qualifications'])->select();
        }

        // 否则继承分类的资质设置
        if ($this->category) {
            return $this->category->qualificationPackages()->with(['qualifications'])->select();
        }

        return collect([]);
    }

    /**
     * 检查商品是否需要特定资质包
     */
    public function requiresQualificationPackage($packageId)
    {
        $packages = $this->getQualificationPackages();
        foreach ($packages as $package) {
            if ($package->package_id == $packageId || $package->id == $packageId) {
                return $package->is_required == 1;
            }
        }
        return false;
    }

    /**
     * 新增后
     */
    public static function onAfterInsert($model)
    {
        // 触发商品创建事件
        Event::trigger('GoodsCreate', $model);
    }

    /**
     * 更新前
     */
    public static function onBeforeUpdate($model)
    {
        // 可以在这里做一些数据验证或处理
    }

    /**
     * 更新后
     */
    public static function onAfterUpdate($model)
    {
        // 触发商品更新事件
        Event::trigger('GoodsUpdate', $model);
    }

    /**
     * 删除前
     */
    public static function onBeforeDelete($model)
    {
        // 可以在这里做一些数据验证或处理
    }

    /**
     * 删除后
     */
    public static function onAfterDelete($model)
    {
        // 触发商品删除事件
        Event::trigger('GoodsDelete', $model);
    }
}