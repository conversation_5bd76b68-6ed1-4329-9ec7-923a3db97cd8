<?php

namespace app\common\model\goods;

use app\common\basics\Models;
use app\model\goods\QualificationPackage;

/**
 * 平台商品分类
 * Class GoodsBrand
 * @package app\common\model\goods
 */
class GoodsCategory extends Models
{
  /**
   * 子分类
   */
  public function sons()
  {
    return $this->hasMany(self::class, 'pid', 'id')->where(['del' => 0]);
  }

  /**
   * 关联分类资质关联表
   */
  public function categoryQualifications()
  {
    return $this->hasMany(GoodsCategoryQualification::class, 'category_id', 'id');
  }

  /**
   * 关联资质包表（通过中间表）
   */
  public function qualificationPackages()
  {
    return $this->belongsToMany(QualificationPackage::class, GoodsCategoryQualification::class, 'package_id', 'category_id');
  }

  /**
   * 关联资质表（通过中间表）- 保留兼容性
   * @deprecated 请使用 qualificationPackages() 方法
   */
  public function qualifications()
  {
    return $this->belongsToMany(Qualification::class, GoodsCategoryQualification::class, 'qualification_id', 'category_id');
  }
}