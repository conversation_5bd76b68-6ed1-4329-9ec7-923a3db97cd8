<?php

namespace app\common\model\goods;

use think\model\Pivot;
use app\model\goods\QualificationPackage;

/**
 * 分类资质关联模型
 * Class GoodsCategoryQualification
 * @package app\common\model\goods
 */
class GoodsCategoryQualification extends Pivot
{
    protected $table = 'ls_goods_category_qualification';

    /**
     * 关联分类表
     */
    public function category()
    {
        return $this->belongsTo(GoodsCategory::class, 'category_id', 'id');
    }

    /**
     * 关联资质包表
     */
    public function package()
    {
        return $this->belongsTo(QualificationPackage::class, 'package_id', 'id');
    }

    /**
     * 关联资质表（保留兼容性，但已废弃）
     * @deprecated 请使用 package() 方法
     */
    public function qualification()
    {
        return $this->belongsTo(Qualification::class, 'qualification_id', 'id');
    }
}
