<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\common\model\goods;

use app\common\model\BaseModel;
use app\model\goods\QualificationPackage;

/**
 * 商品资质覆盖模型
 * Class GoodsQualificationOverride
 * @package app\common\model\goods
 */
class GoodsQualificationOverride extends BaseModel
{
    protected $name = 'goods_qualification_override';
    protected $pk = 'id';
    public $timestamps = false;

    /**
     * 关联商品表
     */
    public function goods()
    {
        return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }

    /**
     * 关联资质包表
     */
    public function package()
    {
        return $this->belongsTo(QualificationPackage::class, 'package_id', 'id');
    }

    /**
     * 获取是否必选文本
     */
    public function getIsRequiredAttr($value)
    {
        $status = [0 => '可选', 1 => '必选'];
        return ['text' => $status[$value], 'value' => $value];
    }

    /**
     * 根据商品ID获取资质包设置
     */
    public static function getByGoodsId($goodsId)
    {
        return self::with(['package.qualifications'])
            ->where('goods_id', $goodsId)
            ->select();
    }

    /**
     * 保存商品资质包设置
     */
    public static function saveGoodsPackages($goodsId, $packages)
    {
        // 删除原有设置
        self::where('goods_id', $goodsId)->delete();
        
        // 添加新设置
        if (!empty($packages)) {
            $data = [];
            foreach ($packages as $package) {
                $data[] = [
                    'goods_id' => $goodsId,
                    'package_id' => $package['package_id'],
                    'is_required' => $package['is_required'] ?? 1
                ];
            }
            self::insertAll($data);
        }
        
        return true;
    }
}
