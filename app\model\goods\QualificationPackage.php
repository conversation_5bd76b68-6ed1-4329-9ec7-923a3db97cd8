<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\model\goods;

use app\common\model\BaseModel;
use app\common\model\goods\Qualification;
use think\model\concern\SoftDelete;

/**
 * 资质包模型
 * Class QualificationPackage
 * @package app\model\goods
 */
class QualificationPackage extends BaseModel
{
    use SoftDelete;
    protected $deleteTime = 'del';
    protected $defaultSoftDelete = 0;

    protected $name = 'qualification_package';

    /**
     * 关联资质包项目
     */
    public function items()
    {
        return $this->hasMany(QualificationPackageItem::class, 'package_id', 'id');
    }

    /**
     * 关联资质
     */
    public function qualifications()
    {
        return $this->belongsToMany(Qualification::class, QualificationPackageItem::class, 'qualification_id', 'package_id');
    }

    public function getSelectionModeAttr($value)
    {
        $status = [1 => '全部需要', 2 => '任选其一'];
        return ['text' => $status[$value], 'value' => $value];
    }
}
