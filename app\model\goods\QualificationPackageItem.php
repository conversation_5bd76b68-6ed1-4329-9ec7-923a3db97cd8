<?php
// +----------------------------------------------------------------------
// | kshop
// +----------------------------------------------------------------------
// | Copyright (c) 2022~2024 kshop All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( https://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: kshop
// +----------------------------------------------------------------------
namespace app\model\goods;

use app\common\model\BaseModel;

/**
 * 资质包条目模型
 * Class QualificationPackageItem
 * @package app\model\goods
 */
class QualificationPackageItem extends BaseModel
{
    protected $name = 'qualification_package_item';
    protected $pk = 'id';
    public $timestamps = false;
}
