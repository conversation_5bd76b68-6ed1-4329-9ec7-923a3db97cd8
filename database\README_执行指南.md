# 商品资质包功能 - 数据库执行指南

## 重要提醒：GTID模式兼容性

如果您的MySQL开启了GTID模式，请使用GTID安全版本的脚本：
- `qualification_package_tables_gtid_safe.sql` （推荐）
- `data_migration_gtid_safe.sql`

如果遇到 "Statement violates GTID consistency" 错误，说明您的MySQL开启了GTID模式。

## 执行顺序

### 1. 备份数据库
```bash
# 建议先完整备份数据库
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 选择合适的脚本版本

#### 方案A：GTID安全版本（推荐）
```bash
# 执行GTID安全版本（适用于所有MySQL版本）
mysql -u用户名 -p密码 数据库名 < qualification_package_tables_gtid_safe.sql
```

#### 方案B：标准版本
```bash
# 执行标准版本（仅适用于未开启GTID的MySQL）
mysql -u用户名 -p密码 数据库名 < qualification_package_tables.sql
```

### 3. 数据迁移（可选）
```bash
# 推荐：使用安全数据迁移脚本（包含完整的前置检查）
mysql -u用户名 -p密码 数据库名 < safe_data_migration.sql

# 或者使用其他版本：
# mysql -u用户名 -p密码 数据库名 < data_migration_gtid_safe.sql
# mysql -u用户名 -p密码 数据库名 < data_migration_qualification_to_package.sql
```

## 可能遇到的问题及解决方案

### 问题1：1786 - Statement violates GTID consistency
**错误信息**：`Statement violates GTID consistency: CREATE TABLE ... SELECT`

**原因**：MySQL开启了GTID模式，不支持 `CREATE TABLE ... SELECT` 语句

**解决方案**：
```bash
# 使用GTID安全版本的脚本
mysql -u用户名 -p密码 数据库名 < qualification_package_tables_gtid_safe.sql
```

### 问题2：1054 - Unknown column 'package_id' in 'where clause'
**原因**：在执行数据迁移时，表结构还没有正确创建或字段不存在

**解决方案**：
```bash
# 使用安全数据迁移脚本，它会自动检查前置条件
mysql -u用户名 -p密码 数据库名 < safe_data_migration.sql

# 或者确保先执行主脚本再执行迁移脚本
mysql -u用户名 -p密码 数据库名 < qualification_package_tables_gtid_safe.sql
mysql -u用户名 -p密码 数据库名 < data_migration_gtid_safe.sql
```

### 问题3：1062 - Duplicate entry 错误
**原因**：`ls_goods_category_qualification` 表中存在重复的分类ID数据

**解决方案**：
```sql
-- 手动清理重复数据
DELETE t1 FROM ls_goods_category_qualification t1
INNER JOIN ls_goods_category_qualification t2
WHERE t1.id > t2.id
AND t1.category_id = t2.category_id
AND t1.qualification_id = t2.qualification_id;
```

### 问题4：表或列已存在
**解决方案**：脚本已包含检查逻辑，会自动跳过已存在的表或列

### 问题5：菜单ID冲突
**解决方案**：
```sql
-- 查看当前最大的菜单ID
SELECT MAX(id) FROM ls_dev_auth;

-- 查看商品管理相关菜单ID
SELECT id, name, uri, type FROM ls_dev_auth WHERE name LIKE '%商品%' ORDER BY type, sort;
```

## 验证安装结果

### 检查表结构
```sql
-- 检查新创建的表
SHOW TABLES LIKE '%qualification_package%';
SHOW TABLES LIKE '%goods_qualification_override%';

-- 检查表结构
DESC ls_qualification_package;
DESC ls_qualification_package_item;
DESC ls_goods_qualification_override;
DESC ls_goods_category_qualification;
DESC ls_goods;
```

### 检查菜单权限
```sql
-- 检查菜单是否正确插入
SELECT * FROM ls_dev_auth WHERE name LIKE '%资质包%' OR uri LIKE '%package%' OR uri LIKE '%qualification%';
```

### 检查数据迁移结果（如果执行了迁移）
```sql
-- 检查迁移统计
SELECT 
    '原始资质数量' as type, COUNT(*) as count FROM ls_qualification WHERE del = 0
UNION ALL
SELECT 
    '转换的资质包数量' as type, COUNT(*) as count FROM ls_qualification_package WHERE name LIKE '%（自动转换）'
UNION ALL
SELECT 
    '资质包项目数量' as type, COUNT(*) as count FROM ls_qualification_package_item
UNION ALL
SELECT 
    '分类资质包关联数量' as type, COUNT(*) as count FROM ls_goods_category_qualification;
```

## 功能测试

1. **访问资质包管理**：`/admin/goods.package/lists`
2. **测试分类资质包设置**：在分类列表中点击"资质包"按钮
3. **测试商品资质包设置**：在商品列表中点击"资质"按钮

## 回滚方案

如果需要回滚，请按以下顺序执行：

```sql
-- 1. 删除新增的菜单
DELETE FROM ls_dev_auth WHERE uri LIKE '%package%' AND uri LIKE 'goods.%';
DELETE FROM ls_dev_auth WHERE uri LIKE '%qualificationPackage%';

-- 2. 恢复原始表结构（如果有备份）
DROP TABLE IF EXISTS ls_qualification_package;
DROP TABLE IF EXISTS ls_qualification_package_item;
DROP TABLE IF EXISTS ls_goods_qualification_override;

-- 3. 恢复分类资质关联表
DROP TABLE IF EXISTS ls_goods_category_qualification;
RENAME TABLE ls_goods_category_qualification_backup TO ls_goods_category_qualification;

-- 4. 删除商品表新增字段
ALTER TABLE ls_goods DROP COLUMN IF EXISTS use_custom_qualification;
```

## 注意事项

1. **生产环境**：请先在测试环境验证所有功能正常后再在生产环境执行
2. **数据备份**：执行前务必备份数据库
3. **权限检查**：确保数据库用户有足够的权限执行DDL操作
4. **业务影响**：建议在业务低峰期执行，避免影响正常业务
