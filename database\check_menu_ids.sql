-- ===================================================================
-- 查找菜单ID辅助脚本
-- 用于确认正确的父级菜单ID，然后手动调整主脚本中的pid值
-- ===================================================================

-- 查找商品管理相关的一级菜单
SELECT 
    id, 
    name, 
    uri, 
    type,
    '一级菜单' as menu_level
FROM ls_dev_auth 
WHERE type = 1 
AND (name LIKE '%商品%' OR uri LIKE '%goods%')
AND del = 0
ORDER BY sort;

-- 查找商品管理相关的二级菜单
SELECT 
    id, 
    pid,
    name, 
    uri, 
    type,
    '二级菜单' as menu_level
FROM ls_dev_auth 
WHERE type = 2 
AND (name LIKE '%商品%' OR name LIKE '%分类%' OR uri LIKE '%goods%' OR uri LIKE '%category%')
AND del = 0
ORDER BY pid, sort;

-- 查找资质管理相关的菜单
SELECT 
    id, 
    pid,
    name, 
    uri, 
    type,
    '资质相关菜单' as menu_level
FROM ls_dev_auth 
WHERE (name LIKE '%资质%' OR uri LIKE '%qualification%')
AND del = 0
ORDER BY type, sort;

-- 建议的菜单结构（请根据上面的查询结果调整）
SELECT '建议的菜单结构：' as info;
SELECT '1. 找到"商品管理"一级菜单的ID，作为"资质包管理"的pid' as step1;
SELECT '2. 找到"商品分类"菜单的ID，作为"分类资质包设置"的pid' as step2;
SELECT '3. 找到"商品列表"或"商品管理"菜单的ID，作为"商品资质包设置"的pid' as step3;

-- 如果需要手动调整，可以使用以下模板：
/*
-- 示例：如果商品管理的ID是25，则修改主脚本中的：
INSERT INTO `ls_dev_auth` (...) VALUES (1, 0, 25, '资质包管理', ...);

-- 示例：如果商品分类的ID是26，则修改主脚本中的：
INSERT INTO `ls_dev_auth` (...) VALUES (2, 0, 26, '分类资质包设置', ...);

-- 示例：如果商品列表的ID是27，则修改主脚本中的：
INSERT INTO `ls_dev_auth` (...) VALUES (2, 0, 27, '商品资质包设置', ...);
*/
