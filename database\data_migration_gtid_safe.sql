-- ===================================================================
-- 数据迁移脚本：将现有的单个资质关联转换为资质包关联 (GTID安全版本)
-- 执行顺序：先执行 qualification_package_tables_gtid_safe.sql，再执行此脚本
-- ===================================================================

-- 检查备份表是否存在
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'ls_goods_category_qualification_backup')
        THEN '备份表存在，可以进行数据迁移'
        ELSE '备份表不存在，请先执行主脚本'
    END as backup_status;

-- 1. 为每个现有的资质创建对应的资质包
-- 这样可以保持向后兼容性
INSERT INTO `ls_qualification_package` (`name`, `selection_mode`, `is_required`, `remark`, `status`, `sort`, `del`, `create_time`, `update_time`)
SELECT 
    CONCAT(q.name, '（自动转换）') as name,
    1 as selection_mode,  -- 默认为全选模式
    1 as is_required,     -- 默认为必选
    CONCAT('由单个资质"', q.name, '"自动转换而来') as remark,
    q.status,
    q.sort,
    0 as del,
    UNIX_TIMESTAMP() as create_time,
    UNIX_TIMESTAMP() as update_time
FROM `ls_qualification` q
WHERE q.del = 0
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package` p 
    WHERE p.name = CONCAT(q.name, '（自动转换）')
);

-- 2. 为每个自动创建的资质包添加对应的资质项目
INSERT INTO `ls_qualification_package_item` (`package_id`, `qualification_id`)
SELECT 
    p.id as package_id,
    q.id as qualification_id
FROM `ls_qualification` q
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE q.del = 0
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package_item` pi 
    WHERE pi.package_id = p.id AND pi.qualification_id = q.id
);

-- 3. 检查表结构是否正确
-- 确保 ls_goods_category_qualification 表有正确的结构
SET @table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
                     WHERE TABLE_SCHEMA = DATABASE()
                     AND TABLE_NAME = 'ls_goods_category_qualification');

SET @package_id_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = DATABASE()
                          AND TABLE_NAME = 'ls_goods_category_qualification'
                          AND COLUMN_NAME = 'package_id');

-- 显示表结构检查结果
SELECT
    CASE WHEN @table_exists > 0 THEN '✓ 表存在' ELSE '✗ 表不存在' END as table_check,
    CASE WHEN @package_id_exists > 0 THEN '✓ package_id字段存在' ELSE '✗ package_id字段不存在' END as column_check;

-- 只有当表结构正确时才进行数据迁移
-- 将备份表中的分类-资质关联转换为分类-资质包关联
INSERT INTO `ls_goods_category_qualification` (`category_id`, `package_id`, `create_time`)
SELECT DISTINCT
    gcq_backup.category_id,
    p.id as package_id,
    COALESCE(gcq_backup.create_time, UNIX_TIMESTAMP()) as create_time
FROM `ls_goods_category_qualification_backup` gcq_backup
INNER JOIN `ls_qualification` q ON gcq_backup.qualification_id = q.id
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE gcq_backup.qualification_id IS NOT NULL
AND @table_exists > 0
AND @package_id_exists > 0
AND NOT EXISTS (
    SELECT 1 FROM `ls_goods_category_qualification` gcq_new
    WHERE gcq_new.category_id = gcq_backup.category_id
    AND gcq_new.package_id = p.id
)
ON DUPLICATE KEY UPDATE create_time = VALUES(create_time);

-- 4. 验证数据迁移结果
SELECT 
    '=== 数据迁移完成 ===' as title;

SELECT 
    '原始数据统计' as category,
    (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) as original_qualifications,
    (SELECT COUNT(*) FROM ls_goods_category_qualification_backup WHERE qualification_id IS NOT NULL) as original_category_relations;

SELECT 
    '转换后数据统计' as category,
    (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）') as converted_packages,
    (SELECT COUNT(*) FROM ls_qualification_package_item) as package_items,
    (SELECT COUNT(*) FROM ls_goods_category_qualification) as new_category_relations;

-- 5. 数据一致性检查
SELECT 
    '数据一致性检查' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) = 
             (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）')
        THEN '✓ 资质包转换数量正确'
        ELSE '✗ 资质包转换数量不匹配'
    END as qualification_package_check,
    CASE 
        WHEN (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）') = 
             (SELECT COUNT(*) FROM ls_qualification_package_item)
        THEN '✓ 资质包项目数量正确'
        ELSE '✗ 资质包项目数量不匹配'
    END as package_item_check;

-- 6. 显示转换示例
SELECT 
    '转换示例（前5个）' as example_title;

SELECT 
    q.name as original_qualification,
    p.name as converted_package,
    p.selection_mode,
    p.is_required
FROM ls_qualification q
INNER JOIN ls_qualification_package p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE q.del = 0
LIMIT 5;

-- 7. 清理建议
SELECT 
    '清理建议' as cleanup_info,
    '1. 验证数据正确性后，可以删除备份表：DROP TABLE ls_goods_category_qualification_backup;' as step1,
    '2. 可以在后台管理界面中调整自动转换的资质包名称和设置' as step2,
    '3. 建议创建新的资质包来替代自动转换的包，以获得更好的管理体验' as step3;

-- 注意事项说明
SELECT 
    '重要提醒' as notice,
    '自动转换的资质包名称包含"（自动转换）"标识' as note1,
    '所有转换的资质包默认设置为"全选"和"必选"模式' as note2,
    '可以在后台手动调整这些设置以符合实际业务需求' as note3;
