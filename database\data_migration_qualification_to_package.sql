-- ===================================================================
-- 数据迁移脚本：将现有的单个资质关联转换为资质包关联
-- 执行顺序：先执行 qualification_package_tables.sql，再执行此脚本
-- ===================================================================

-- 1. 为每个现有的资质创建对应的资质包
-- 这样可以保持向后兼容性（兼容GTID模式）
INSERT INTO `ls_qualification_package` (`name`, `selection_mode`, `is_required`, `remark`, `status`, `sort`, `del`, `create_time`, `update_time`)
SELECT
    CONCAT(q.name, '（自动转换）') as name,
    1 as selection_mode,  -- 默认为全选模式
    1 as is_required,     -- 默认为必选
    CONCAT('由单个资质"', q.name, '"自动转换而来') as remark,
    q.status,
    q.sort,
    0 as del,
    UNIX_TIMESTAMP() as create_time,
    UNIX_TIMESTAMP() as update_time
FROM `ls_qualification` q
WHERE q.del = 0
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package` p
    WHERE p.name = CONCAT(q.name, '（自动转换）')
);

-- 2. 为每个自动创建的资质包添加对应的资质项目
INSERT INTO `ls_qualification_package_item` (`package_id`, `qualification_id`)
SELECT 
    p.id as package_id,
    q.id as qualification_id
FROM `ls_qualification` q
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE q.del = 0
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package_item` pi 
    WHERE pi.package_id = p.id AND pi.qualification_id = q.id
);

-- 3. 将备份表中的分类-资质关联转换为分类-资质包关联
-- 注意：这里假设备份表存在且有数据
INSERT INTO `ls_goods_category_qualification` (`category_id`, `package_id`, `create_time`)
SELECT DISTINCT
    gcq_backup.category_id,
    p.id as package_id,
    UNIX_TIMESTAMP() as create_time
FROM `ls_goods_category_qualification_backup` gcq_backup
INNER JOIN `ls_qualification` q ON gcq_backup.qualification_id = q.id
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE NOT EXISTS (
    SELECT 1 FROM `ls_goods_category_qualification` gcq_new 
    WHERE gcq_new.category_id = gcq_backup.category_id 
    AND gcq_new.package_id = p.id
)
ON DUPLICATE KEY UPDATE create_time = VALUES(create_time);

-- 4. 验证数据迁移结果
SELECT 
    '数据迁移完成' as status,
    (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) as original_qualifications,
    (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）') as converted_packages,
    (SELECT COUNT(*) FROM ls_qualification_package_item) as package_items,
    (SELECT COUNT(*) FROM ls_goods_category_qualification) as category_package_relations;

-- 5. 清理说明
-- 迁移完成后，可以选择删除备份表（建议先验证数据正确性）
-- DROP TABLE IF EXISTS `ls_goods_category_qualification_backup`;

-- 注意事项：
-- 1. 执行前请确保已经备份了数据库
-- 2. 建议在测试环境先执行验证
-- 3. 自动转换的资质包名称包含"（自动转换）"标识，可以在后台手动调整
-- 4. 如果不需要保持向后兼容，可以手动创建新的资质包来替代自动转换的包
