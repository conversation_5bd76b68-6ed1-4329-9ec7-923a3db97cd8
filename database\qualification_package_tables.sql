-- ===================================================================
-- 资质包功能相关表结构及修改
-- 执行说明：
-- 1. 请先备份数据库
-- 2. 如果遇到重复键错误，请先执行数据清理
-- 3. 执行完成后可运行 data_migration_qualification_to_package.sql 进行数据迁移
-- ===================================================================

-- ----------------------------
-- Table structure for ls_qualification_package (资质包/条目表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_qualification_package`;
CREATE TABLE `ls_qualification_package` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质包名称',
  `selection_mode` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '选择模式: 1-全选, 2-任选其一',
  `is_required` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否必选: 1-必选, 0-可选',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '资质包描述',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:1-是;0-否',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_del` (`status`, `del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资质包（资质条目）表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_qualification_package_item (资质包-证书关联表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_qualification_package_item`;
CREATE TABLE `ls_qualification_package_item` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `package_id` int(11) UNSIGNED NOT NULL COMMENT '资质包ID',
  `qualification_id` int(11) UNSIGNED NOT NULL COMMENT '资质ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_package_qualification` (`package_id`, `qualification_id`) USING BTREE,
  INDEX `idx_package_id` (`package_id`) USING BTREE,
  INDEX `idx_qualification_id` (`qualification_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资质包-证书关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_goods_qualification_override (商品资质覆盖表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_qualification_override`;
CREATE TABLE `ls_goods_qualification_override` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `package_id` int(11) unsigned NOT NULL COMMENT '资质包ID',
  `is_required` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否必选: 1-必选, 0-可选',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_package_id` (`package_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品资质覆盖规则表';


-- ----------------------------
-- Modify table ls_goods_category_qualification
-- ----------------------------
-- 检查并备份原有数据（兼容GTID模式）
DROP TABLE IF EXISTS `ls_goods_category_qualification_backup`;

-- 创建备份表结构
CREATE TABLE `ls_goods_category_qualification_backup` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `qualification_id` int(11) unsigned DEFAULT NULL COMMENT '资质ID（将被删除）',
  `package_id` int(11) unsigned DEFAULT NULL COMMENT '资质包ID',
  `create_time` int(11) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类资质关联备份表';

-- 插入备份数据
INSERT INTO `ls_goods_category_qualification_backup`
SELECT * FROM `ls_goods_category_qualification`;

-- 清空原表数据，避免重复键冲突
TRUNCATE TABLE `ls_goods_category_qualification`;

-- 检查并添加package_id列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ls_goods_category_qualification'
     AND COLUMN_NAME = 'package_id') = 0,
    'ALTER TABLE `ls_goods_category_qualification` ADD COLUMN `package_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''资质包ID'' AFTER `category_id`',
    'SELECT "package_id column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除原有的qualification_id列（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ls_goods_category_qualification'
     AND COLUMN_NAME = 'qualification_id') > 0,
    'ALTER TABLE `ls_goods_category_qualification` DROP COLUMN `qualification_id`',
    'SELECT "qualification_id column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除原有索引（如果存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ls_goods_category_qualification'
     AND INDEX_NAME = 'uk_category_qualification') > 0,
    'ALTER TABLE `ls_goods_category_qualification` DROP INDEX `uk_category_qualification`',
    'SELECT "uk_category_qualification index does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除可能存在的uk_category_package索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ls_goods_category_qualification'
     AND INDEX_NAME = 'uk_category_package') > 0,
    'ALTER TABLE `ls_goods_category_qualification` DROP INDEX `uk_category_package`',
    'SELECT "uk_category_package index does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加新的唯一索引
ALTER TABLE `ls_goods_category_qualification`
ADD UNIQUE KEY `uk_category_package` (`category_id`, `package_id`);


-- ----------------------------
-- Modify table ls_goods
-- ----------------------------
-- 检查并添加use_custom_qualification列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'ls_goods'
     AND COLUMN_NAME = 'use_custom_qualification') = 0,
    'ALTER TABLE `ls_goods` ADD COLUMN `use_custom_qualification` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''是否使用商品独立资质: 1-是, 0-否(继承分类)''',
    'SELECT "use_custom_qualification column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


-- ----------------------------
-- 菜单权限数据
-- 注意：请根据实际的菜单ID调整pid值
-- 29: 商品管理菜单ID（请确认）
-- 28: 商品分类菜单ID（请确认）
-- 30: 商品列表菜单ID（请确认）
-- ----------------------------

-- 查找商品管理相关的菜单ID（用于确认pid）
-- SELECT id, name, uri FROM ls_dev_auth WHERE name LIKE '%商品%' AND type = 1;

-- 资质包管理菜单（添加到商品管理下）
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`)
VALUES (1, 0, 29, '资质包管理', 'layui-icon-component', 'goods.package/lists', 25, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    icon = VALUES(icon),
    uri = VALUES(uri),
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 获取资质包管理菜单ID
SET @package_menu_id = (SELECT id FROM ls_dev_auth WHERE uri = 'goods.package/lists' AND type = 1 LIMIT 1);

-- 资质包管理子菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) VALUES
(2, 0, @package_menu_id, '资质包列表', '', 'goods.package/lists', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '添加资质包', '', 'goods.package/add', 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '编辑资质包', '', 'goods.package/edit', 3, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '删除资质包', '', 'goods.package/del', 4, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '资质包状态', '', 'goods.package/status', 5, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '获取启用资质包', '', 'goods.package/getEnabled', 6, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 分类资质包设置权限（添加到商品分类下）
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`)
VALUES (2, 0, 28, '分类资质包设置', '', 'goods.category/qualificationPackage', 10, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 商品资质包设置权限（添加到商品管理下）
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`)
VALUES (2, 0, 30, '商品资质包设置', '', 'goods.goods/qualificationPackage', 15, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 显示插入结果
SELECT '菜单权限插入完成' as status, COUNT(*) as total_menus
FROM ls_dev_auth
WHERE uri LIKE '%package%' OR uri LIKE '%qualificationPackage%';
