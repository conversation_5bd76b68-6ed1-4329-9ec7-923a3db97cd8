-- ===================================================================
-- 资质包功能相关表结构及修改 (GTID安全版本)
-- 此版本兼容MySQL GTID模式，避免使用 CREATE TABLE ... SELECT 语句
-- ===================================================================

-- ----------------------------
-- Table structure for ls_qualification_package (资质包/条目表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_qualification_package`;
CREATE TABLE `ls_qualification_package` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资质包名称',
  `selection_mode` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '选择模式: 1-全选, 2-任选其一',
  `is_required` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否必选: 1-必选, 0-可选',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '资质包描述',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `sort` int(11) UNSIGNED NULL DEFAULT 255 COMMENT '排序',
  `del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:1-是;0-否',
  `create_time` int(11) UNSIGNED NOT NULL COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_del` (`status`, `del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资质包（资质条目）表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_qualification_package_item (资质包-证书关联表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_qualification_package_item`;
CREATE TABLE `ls_qualification_package_item` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `package_id` int(11) UNSIGNED NOT NULL COMMENT '资质包ID',
  `qualification_id` int(11) UNSIGNED NOT NULL COMMENT '资质ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_package_qualification` (`package_id`, `qualification_id`) USING BTREE,
  INDEX `idx_package_id` (`package_id`) USING BTREE,
  INDEX `idx_qualification_id` (`qualification_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资质包-证书关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ls_goods_qualification_override (商品资质覆盖表)
-- ----------------------------
DROP TABLE IF EXISTS `ls_goods_qualification_override`;
CREATE TABLE `ls_goods_qualification_override` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) unsigned NOT NULL COMMENT '商品ID',
  `package_id` int(11) unsigned NOT NULL COMMENT '资质包ID',
  `is_required` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否必选: 1-必选, 0-可选',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_package_id` (`package_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品资质覆盖规则表';

-- ----------------------------
-- 备份并修改 ls_goods_category_qualification 表
-- ----------------------------

-- 第一步：创建备份表结构
DROP TABLE IF EXISTS `ls_goods_category_qualification_backup`;
CREATE TABLE `ls_goods_category_qualification_backup` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `qualification_id` int(11) unsigned DEFAULT NULL COMMENT '资质ID（原字段）',
  `package_id` int(11) unsigned DEFAULT NULL COMMENT '资质包ID（新字段）',
  `create_time` int(11) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_qualification_id` (`qualification_id`),
  KEY `idx_package_id` (`package_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类资质关联备份表';

-- 第二步：备份原有数据
INSERT INTO `ls_goods_category_qualification_backup` 
SELECT * FROM `ls_goods_category_qualification`;

-- 第三步：重建原表结构
DROP TABLE IF EXISTS `ls_goods_category_qualification`;
CREATE TABLE `ls_goods_category_qualification` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(11) unsigned NOT NULL COMMENT '分类ID',
  `package_id` int(11) unsigned NOT NULL COMMENT '资质包ID',
  `create_time` int(11) unsigned DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_package` (`category_id`, `package_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_package_id` (`package_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类资质包关联表';

-- ----------------------------
-- 修改 ls_goods 表
-- ----------------------------
-- 检查并添加use_custom_qualification列
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'ls_goods' 
     AND COLUMN_NAME = 'use_custom_qualification') = 0,
    'ALTER TABLE `ls_goods` ADD COLUMN `use_custom_qualification` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT ''是否使用商品独立资质: 1-是, 0-否(继承分类)''',
    'SELECT "use_custom_qualification column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ----------------------------
-- 菜单权限数据
-- ----------------------------

-- 查找商品管理菜单的ID作为父级菜单
SET @goods_management_id = (SELECT id FROM ls_dev_auth WHERE name LIKE '%商品管理%' AND type = 1 AND del = 0 LIMIT 1);
SET @goods_category_id = (SELECT id FROM ls_dev_auth WHERE (name LIKE '%商品分类%' OR uri LIKE '%category%') AND type = 2 AND del = 0 LIMIT 1);
SET @goods_list_id = (SELECT id FROM ls_dev_auth WHERE (name LIKE '%商品列表%' OR name LIKE '%商品管理%') AND uri LIKE '%goods%' AND type = 2 AND del = 0 LIMIT 1);

-- 如果找不到对应的菜单ID，使用默认值（请根据实际情况调整）
SET @goods_management_id = IFNULL(@goods_management_id, 29);
SET @goods_category_id = IFNULL(@goods_category_id, 28);
SET @goods_list_id = IFNULL(@goods_list_id, 30);

-- 资质包管理主菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES (1, 0, @goods_management_id, '资质包管理', 'layui-icon-component', 'goods.package/lists', 25, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name), 
    icon = VALUES(icon), 
    uri = VALUES(uri), 
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 获取资质包管理菜单ID
SET @package_menu_id = (SELECT id FROM ls_dev_auth WHERE uri = 'goods.package/lists' AND type = 1 LIMIT 1);

-- 资质包管理子菜单
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) VALUES
(2, 0, @package_menu_id, '资质包列表', '', 'goods.package/lists', 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '添加资质包', '', 'goods.package/add', 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '编辑资质包', '', 'goods.package/edit', 3, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '删除资质包', '', 'goods.package/del', 4, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '资质包状态', '', 'goods.package/status', 5, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 0, @package_menu_id, '获取启用资质包', '', 'goods.package/getEnabled', 6, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name), 
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 分类资质包设置权限
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES (2, 0, @goods_category_id, '分类资质包设置', '', 'goods.category/qualificationPackage', 10, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name), 
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 商品资质包设置权限
INSERT INTO `ls_dev_auth` (`type`, `system`, `pid`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`, `update_time`, `del`) 
VALUES (2, 0, @goods_list_id, '商品资质包设置', '', 'goods.goods/qualificationPackage', 15, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0)
ON DUPLICATE KEY UPDATE 
    name = VALUES(name), 
    sort = VALUES(sort),
    update_time = VALUES(update_time);

-- 显示执行结果
SELECT
    '数据库结构创建完成' as status,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME LIKE '%qualification_package%') as new_tables,
    (SELECT COUNT(*) FROM ls_dev_auth WHERE uri LIKE '%package%' OR uri LIKE '%qualificationPackage%') as menu_items,
    @goods_management_id as goods_management_menu_id,
    @goods_category_id as goods_category_menu_id,
    @goods_list_id as goods_list_menu_id;

-- 验证表结构
SELECT
    '表结构验证' as check_type,
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    DATA_TYPE as data_type
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('ls_qualification_package', 'ls_qualification_package_item', 'ls_goods_qualification_override', 'ls_goods_category_qualification')
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 提示下一步操作
SELECT
    '下一步操作提示' as next_steps,
    '1. 如需迁移现有数据，请执行: data_migration_gtid_safe.sql' as step1,
    '2. 验证功能正常后，可删除备份表: DROP TABLE ls_goods_category_qualification_backup;' as step2,
    '3. 访问后台管理界面测试资质包功能' as step3;
