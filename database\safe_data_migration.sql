-- ===================================================================
-- 安全数据迁移脚本：将现有的单个资质关联转换为资质包关联
-- 此脚本包含完整的前置条件检查，确保安全执行
-- ===================================================================

-- 第一步：环境检查
SELECT '=== 环境检查开始 ===' as step;

-- 检查必要的表是否存在
SET @backup_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                            WHERE TABLE_SCHEMA = DATABASE() 
                            AND TABLE_NAME = 'ls_goods_category_qualification_backup');

SET @qualification_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                                   WHERE TABLE_SCHEMA = DATABASE() 
                                   AND TABLE_NAME = 'ls_qualification');

SET @package_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                             WHERE TABLE_SCHEMA = DATABASE() 
                             AND TABLE_NAME = 'ls_qualification_package');

SET @package_item_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                                  WHERE TABLE_SCHEMA = DATABASE() 
                                  AND TABLE_NAME = 'ls_qualification_package_item');

SET @category_qual_table_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                                   WHERE TABLE_SCHEMA = DATABASE() 
                                   AND TABLE_NAME = 'ls_goods_category_qualification');

-- 检查新表的字段结构
SET @package_id_column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                                 WHERE TABLE_SCHEMA = DATABASE() 
                                 AND TABLE_NAME = 'ls_goods_category_qualification' 
                                 AND COLUMN_NAME = 'package_id');

-- 显示检查结果
SELECT 
    '表存在性检查' as check_type,
    CASE WHEN @backup_table_exists > 0 THEN '✓' ELSE '✗' END as backup_table,
    CASE WHEN @qualification_table_exists > 0 THEN '✓' ELSE '✗' END as qualification_table,
    CASE WHEN @package_table_exists > 0 THEN '✓' ELSE '✗' END as package_table,
    CASE WHEN @package_item_table_exists > 0 THEN '✓' ELSE '✗' END as package_item_table,
    CASE WHEN @category_qual_table_exists > 0 THEN '✓' ELSE '✗' END as category_qual_table,
    CASE WHEN @package_id_column_exists > 0 THEN '✓' ELSE '✗' END as package_id_column;

-- 检查是否可以继续执行
SET @can_proceed = (@backup_table_exists > 0 AND @qualification_table_exists > 0 AND 
                    @package_table_exists > 0 AND @package_item_table_exists > 0 AND 
                    @category_qual_table_exists > 0 AND @package_id_column_exists > 0);

SELECT 
    CASE 
        WHEN @can_proceed = 1 THEN '✓ 所有前置条件满足，可以继续执行数据迁移'
        ELSE '✗ 前置条件不满足，请先执行主脚本 qualification_package_tables_gtid_safe.sql'
    END as proceed_status;

-- 第二步：数据统计（仅在可以继续时执行）
SELECT '=== 数据统计 ===' as step;

SELECT 
    '迁移前数据统计' as stats_type,
    (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) as active_qualifications,
    (SELECT COUNT(*) FROM ls_goods_category_qualification_backup WHERE qualification_id IS NOT NULL) as backup_relations,
    (SELECT COUNT(*) FROM ls_qualification_package) as existing_packages,
    (SELECT COUNT(*) FROM ls_qualification_package_item) as existing_package_items,
    (SELECT COUNT(*) FROM ls_goods_category_qualification) as current_relations;

-- 第三步：执行数据迁移（仅在前置条件满足时）

-- 3.1 为每个现有的资质创建对应的资质包
INSERT INTO `ls_qualification_package` (`name`, `selection_mode`, `is_required`, `remark`, `status`, `sort`, `del`, `create_time`, `update_time`)
SELECT 
    CONCAT(q.name, '（自动转换）') as name,
    1 as selection_mode,  -- 默认为全选模式
    1 as is_required,     -- 默认为必选
    CONCAT('由单个资质"', q.name, '"自动转换而来') as remark,
    q.status,
    q.sort,
    0 as del,
    UNIX_TIMESTAMP() as create_time,
    UNIX_TIMESTAMP() as update_time
FROM `ls_qualification` q
WHERE q.del = 0
AND @can_proceed = 1
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package` p 
    WHERE p.name = CONCAT(q.name, '（自动转换）')
);

-- 3.2 为每个自动创建的资质包添加对应的资质项目
INSERT INTO `ls_qualification_package_item` (`package_id`, `qualification_id`)
SELECT 
    p.id as package_id,
    q.id as qualification_id
FROM `ls_qualification` q
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE q.del = 0
AND @can_proceed = 1
AND NOT EXISTS (
    SELECT 1 FROM `ls_qualification_package_item` pi 
    WHERE pi.package_id = p.id AND pi.qualification_id = q.id
);

-- 3.3 将备份表中的分类-资质关联转换为分类-资质包关联
INSERT INTO `ls_goods_category_qualification` (`category_id`, `package_id`, `create_time`)
SELECT DISTINCT
    gcq_backup.category_id,
    p.id as package_id,
    COALESCE(gcq_backup.create_time, UNIX_TIMESTAMP()) as create_time
FROM `ls_goods_category_qualification_backup` gcq_backup
INNER JOIN `ls_qualification` q ON gcq_backup.qualification_id = q.id
INNER JOIN `ls_qualification_package` p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE gcq_backup.qualification_id IS NOT NULL
AND @can_proceed = 1
ON DUPLICATE KEY UPDATE create_time = VALUES(create_time);

-- 第四步：验证迁移结果
SELECT '=== 迁移结果验证 ===' as step;

SELECT 
    '迁移后数据统计' as stats_type,
    (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) as original_qualifications,
    (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）') as converted_packages,
    (SELECT COUNT(*) FROM ls_qualification_package_item) as total_package_items,
    (SELECT COUNT(*) FROM ls_goods_category_qualification) as new_relations;

-- 数据一致性检查
SELECT 
    '数据一致性检查' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM ls_qualification WHERE del = 0) = 
             (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）')
        THEN '✓ 资质包转换数量正确'
        ELSE '✗ 资质包转换数量不匹配'
    END as qualification_package_check,
    CASE 
        WHEN (SELECT COUNT(*) FROM ls_qualification_package WHERE name LIKE '%（自动转换）') <= 
             (SELECT COUNT(*) FROM ls_qualification_package_item)
        THEN '✓ 资质包项目数量合理'
        ELSE '✗ 资质包项目数量异常'
    END as package_item_check;

-- 第五步：显示转换示例
SELECT '=== 转换示例 ===' as step;

SELECT 
    q.name as original_qualification,
    p.name as converted_package,
    CASE p.selection_mode WHEN 1 THEN '全部需要' WHEN 2 THEN '任选其一' END as selection_mode,
    CASE p.is_required WHEN 1 THEN '必选' WHEN 0 THEN '可选' END as is_required
FROM ls_qualification q
INNER JOIN ls_qualification_package p ON p.name = CONCAT(q.name, '（自动转换）')
WHERE q.del = 0
LIMIT 5;

-- 第六步：后续操作建议
SELECT '=== 后续操作建议 ===' as step;

SELECT 
    '清理和优化建议' as suggestions,
    '1. 验证数据正确性后，可删除备份表：DROP TABLE ls_goods_category_qualification_backup;' as step1,
    '2. 在后台管理界面调整自动转换的资质包名称，去掉"（自动转换）"标识' as step2,
    '3. 根据业务需求调整资质包的选择模式和必选设置' as step3,
    '4. 考虑创建新的资质包来替代自动转换的包，以获得更好的管理体验' as step4,
    '5. 测试分类和商品的资质包设置功能' as step5;

SELECT 
    CASE 
        WHEN @can_proceed = 1 THEN '✓ 数据迁移执行完成'
        ELSE '✗ 数据迁移未执行，请检查前置条件'
    END as final_status;
